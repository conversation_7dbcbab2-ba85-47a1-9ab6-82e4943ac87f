import React, { createContext, useContext, useEffect, useState } from 'react';
import socketService from '@/lib/socket';
import { SocketConnectionStatus } from '@/lib/types';

interface SocketContextType {
  connectionStatus: SocketConnectionStatus;
  isConnected: boolean;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

export const useSocketContext = () => {
  const context = useContext(SocketContext);
  if (context === undefined) {
    throw new Error('useSocketContext must be used within a SocketProvider');
  }
  return context;
};

interface SocketProviderProps {
  children: React.ReactNode;
}

export const SocketProvider: React.FC<SocketProviderProps> = ({ children }) => {
  const [connectionStatus, setConnectionStatus] = useState<SocketConnectionStatus>({
    connected: false,
  });

  useEffect(() => {
    // Initialize socket connection asynchronously to avoid blocking the app
    const initializeSocket = async () => {
      try {
        // Add a small delay to ensure the app renders first
        await new Promise(resolve => setTimeout(resolve, 100));

        const socket = socketService.connect();

        const handleConnect = () => {
          console.log('Socket connected');
          setConnectionStatus({ connected: true });
        };

        const handleDisconnect = (reason: string) => {
          console.log('Socket disconnected:', reason);
          setConnectionStatus({
            connected: false,
            error: `Disconnected: ${reason}`
          });
        };

        const handleConnectError = (error: any) => {
          console.error('Socket connection error:', error);
          setConnectionStatus({
            connected: false,
            error: `Connection error: ${error.message || error}`
          });
        };

        // Set up event listeners
        socket.on('connect', handleConnect);
        socket.on('disconnect', handleDisconnect);
        socket.on('connect_error', handleConnectError);

        // Set initial connection status
        setConnectionStatus({ connected: socket.connected });

        // Return cleanup function
        return () => {
          try {
            socket.off('connect', handleConnect);
            socket.off('disconnect', handleDisconnect);
            socket.off('connect_error', handleConnectError);
            socketService.disconnect();
          } catch (error) {
            console.error('Error during socket cleanup:', error);
          }
        };
      } catch (error) {
        console.error('Error initializing socket:', error);
        setConnectionStatus({
          connected: false,
          error: `Initialization error: ${error}`
        });
        return () => {}; // Return empty cleanup function
      }
    };

    let cleanup: (() => void) | undefined;

    initializeSocket().then((cleanupFn) => {
      cleanup = cleanupFn;
    });

    // Return cleanup function
    return () => {
      if (cleanup) {
        cleanup();
      }
    };
  }, []);

  const value: SocketContextType = {
    connectionStatus,
    isConnected: connectionStatus.connected,
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};
